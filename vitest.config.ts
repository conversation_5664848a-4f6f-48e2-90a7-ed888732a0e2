import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./tests/setup.ts'],
    env: {
      NODE_ENV: 'test',
      DATABASE_URL: 'postgresql://postgres:postgres@localhost:5432/rsdh_test',
      ADMIN_EMAIL: '<EMAIL>',
      ADMIN_PASSWORD: 'testpassword123',
      TEST_EMAIL: '<EMAIL>',
      TEST_PASSWORD: 'testpassword123',
      JWT_SECRET: 'test-jwt-secret',
    },
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        'tests/',
        '**/*.d.ts',
        'vitest.config.ts',
        'prisma/',
      ],
    },
    testTimeout: 10000,
    pool: 'forks', // Use forks to isolate tests better
  },
});
