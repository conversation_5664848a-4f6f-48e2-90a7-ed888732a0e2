// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id              String             @id @default(uuid())
  email           String             @unique
  name            String?
  avatar          String?            // User avatar URL
  emailVerified   <PERSON>olean            @default(false)
  disabled        <PERSON>olean            @default(false) // User disabled flag
  password        String
  role            String             @default("user") // user, admin
  createdAt       DateTime           @default(now()) @map("created_at")
  updatedAt       DateTime           @updatedAt @map("updated_at")
  sessions        Session[]
  refreshTokens   RefreshToken[]
  resetTokens     PasswordResetToken[]

  @@map("users")
  @@index([email])
  @@index([role])
}

// Session management
model Session {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  userAgent       String?  @map("user_agent")
  ipAddress       String?  @map("ip_address")
  expiresAt       DateTime @map("expires_at")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("sessions")
  @@index([userId])
  @@index([token])
}

// Refresh tokens for long-lived sessions
model RefreshToken {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  expiresAt       DateTime @map("expires_at")
  createdAt       DateTime @default(now()) @map("created_at")

  @@map("refresh_tokens")
  @@index([userId])
  @@index([token])
}

// Password reset tokens
model PasswordResetToken {
  id              String   @id @default(uuid())
  userId          String   @map("user_id")
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token           String   @unique
  expiresAt       DateTime @map("expires_at")
  used            Boolean  @default(false)
  createdAt       DateTime @default(now()) @map("created_at")

  @@map("password_reset_tokens")
  @@index([userId])
  @@index([token])
}

// Verification codes for email verification, registration, password reset
model VerificationCode {
  id        String   @id @default(uuid())
  email     String
  code      String
  type      String   // 'registration', 'password-reset', 'email-verification'
  expiresAt DateTime @map("expires_at")
  used      Boolean  @default(false)
  createdAt DateTime @default(now()) @map("created_at")

  @@map("verification_codes")
  @@index([email, type])
  @@index([code])
  @@index([expiresAt])
}
