import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { FastifyInstance } from 'fastify';
import { createApp } from '../../src/app';

describe('API Integration Tests', () => {
  let app: FastifyInstance;
  let adminToken: string;
  let userToken: string;

  beforeAll(async () => {
    // Create app instance
    app = await createApp();
    await app.ready();

    // Login as admin to get token
    const adminLoginResponse = await app.inject({
      method: 'POST',
      url: '/api/auth/login',
      payload: {
        email: '<EMAIL>',
        password: 'Test12345'
      }
    });

    expect(adminLoginResponse.statusCode).toBe(200);
    const adminLoginData = JSON.parse(adminLoginResponse.payload);
    adminToken = adminLoginData.token;

    // Login as test user to get token
    const userLoginResponse = await app.inject({
      method: 'POST',
      url: '/api/auth/login',
      payload: {
        email: 'eric<PERSON>@diff-lab.com',
        password: 'Test1234'
      }
    });

    expect(userLoginResponse.statusCode).toBe(200);
    const userLoginData = JSON.parse(userLoginResponse.payload);
    userToken = userLoginData.token;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication Routes', () => {
    it('should login with valid credentials', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'Test12345'
        }
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload);
      expect(data.message).toBe('Login successful');
      expect(data.token).toBeDefined();
      expect(data.user.email).toBe('<EMAIL>');
    });

    it('should reject invalid credentials', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'wrongpassword'
        }
      });

      expect(response.statusCode).toBe(401);
      const data = JSON.parse(response.payload);
      expect(data.error).toBe('Invalid Credentials');
    });
  });

  describe('User Profile Routes', () => {
    it('should get current user profile with valid token', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users/profile',
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload);
      expect(data.email).toBe('<EMAIL>');
      expect(data.role).toBe('admin');
    });

    it('should reject request without token', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users/profile'
      });

      expect(response.statusCode).toBe(401);
    });

    it('should update user profile', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: '/api/users/profile',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          name: 'Updated Test User',
          avatar: 'https://example.com/avatar.jpg'
        }
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload);
      expect(data.name).toBe('Updated Test User');
      expect(data.avatar).toBe('https://example.com/avatar.jpg');
    });
  });

  describe('Admin User Management Routes', () => {
    it('should get all users as admin', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users',
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload);
      expect(data.users).toBeDefined();
      expect(Array.isArray(data.users)).toBe(true);
      expect(data.total).toBeGreaterThan(0);
    });

    it('should reject non-admin access to user list', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(response.statusCode).toBe(403);
    });

    it('should get user by ID as admin', async () => {
      // First get the user list to get a user ID
      const usersResponse = await app.inject({
        method: 'GET',
        url: '/api/users',
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      const usersData = JSON.parse(usersResponse.payload);
      const userId = usersData.users[0].id;

      const response = await app.inject({
        method: 'GET',
        url: `/api/users/${userId}`,
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload);
      expect(data.id).toBe(userId);
    });
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/health'
      });

      expect(response.statusCode).toBe(200);
      const data = JSON.parse(response.payload);
      expect(data.status).toBe('ok');
    });
  });
});
