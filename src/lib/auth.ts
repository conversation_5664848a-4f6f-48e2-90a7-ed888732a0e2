import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { prisma } from './prisma';

const trustedOrigins = process.env.TRUSTED_ORIGINS?.split(',') || [];

export const auth = betterAuth({
  trustedOrigins,
  database: prismaAdapter(prisma, { provider: 'postgresql' }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true if you want email verification
    sendResetPassword: async ({ user, url, token }) => {
      // TODO: Implement email sending logic
      console.log(`Password reset requested for ${user.email}`);
      console.log(`Reset URL: ${url}`);
      console.log(`Reset Token: ${token}`);
      // In production, you would send an email here
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // Update session every 24 hours
  },
  user: {
    additionalFields: {
      avatar: {
        type: 'string',
        required: false,
      },
      disabled: {
        type: 'boolean',
        required: false,
        defaultValue: false,
      },
      role: {
        type: 'string',
        required: false,
        defaultValue: 'user',
      },
    },
  },
});
