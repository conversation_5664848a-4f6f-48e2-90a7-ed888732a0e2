import { FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../services/userService';

export interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    email: string;
    role: string;
    disabled: boolean;
  };
}

/**
 * Simple authentication middleware
 * For now, we'll use a basic approach until better-auth is fully integrated
 */
export async function authenticateUser(request: AuthenticatedRequest, reply: FastifyReply) {
  try {
    // For development/testing, we'll use a simple header-based auth
    // In production, this should be replaced with proper JWT validation
    const authHeader = request.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      });
    }

    // Extract email from the token (simplified for now)
    const token = authHeader.substring(7); // Remove 'Bearer '
    
    // For development, we'll decode a simple base64 encoded email
    // In production, this should be proper JWT validation
    let email: string;
    try {
      email = Buffer.from(token, 'base64').toString('utf-8');
    } catch (error) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Invalid token format'
      });
    }

    // Get user from database
    const user = await UserService.getUserByEmail(email);
    if (!user) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'User not found'
      });
    }

    if (user.disabled) {
      return reply.status(403).send({
        error: 'Forbidden',
        message: 'User account is disabled'
      });
    }

    // Attach user to request
    request.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      disabled: user.disabled
    };

  } catch (error) {
    request.log.error('Authentication error:', error);
    return reply.status(500).send({
      error: 'Internal Server Error',
      message: 'Authentication failed'
    });
  }
}

/**
 * Admin role check middleware
 */
export async function requireAdmin(request: AuthenticatedRequest, reply: FastifyReply) {
  if (!request.user) {
    return reply.status(401).send({
      error: 'Unauthorized',
      message: 'Authentication required'
    });
  }

  if (request.user.role !== 'admin') {
    return reply.status(403).send({
      error: 'Forbidden',
      message: 'Admin access required'
    });
  }
}

/**
 * Generate a simple token for development/testing
 */
export function generateSimpleToken(email: string): string {
  return Buffer.from(email).toString('base64');
}
