import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../services/userService';
import { UpdateUserInput } from '../types/user';

interface GetUserParams {
  id: string;
}

interface UpdateUserBody extends UpdateUserInput {}

interface GetUsersQuery {
  page?: number;
  limit?: number;
}

interface ToggleUserStatusBody {
  disabled: boolean;
}

interface UpdateUserRoleBody {
  role: string;
}

export async function userRoutes(fastify: FastifyInstance) {
  // Get current user profile
  fastify.get('/profile', {
    schema: {
      tags: ['Users'],
      summary: 'Get current user profile',
      description: 'Get the profile of the currently authenticated user',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'User profile',
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            avatar: { type: 'string' },
            emailVerified: { type: 'boolean' },
            disabled: { type: 'boolean' },
            role: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        401: {
          description: 'Unauthorized',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // TODO: Extract user ID from session/JWT
    // For now, return a placeholder response
    reply.status(501).send({ error: 'Not implemented', message: 'Session validation not yet implemented' });
  });

  // Update current user profile
  fastify.put('/profile', {
    schema: {
      tags: ['Users'],
      summary: 'Update current user profile',
      description: 'Update the profile of the currently authenticated user',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          avatar: { type: 'string', format: 'uri' },
        },
      },
      response: {
        200: {
          description: 'Updated user profile',
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            avatar: { type: 'string' },
            emailVerified: { type: 'boolean' },
            disabled: { type: 'boolean' },
            role: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        401: {
          description: 'Unauthorized',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Body: UpdateUserBody }>, reply: FastifyReply) => {
    // TODO: Extract user ID from session/JWT and update profile
    reply.status(501).send({ error: 'Not implemented', message: 'Session validation not yet implemented' });
  });

  // Get user by ID (admin only)
  fastify.get('/:id', {
    schema: {
      tags: ['Users'],
      summary: 'Get user by ID',
      description: 'Get a user by their ID (admin only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      response: {
        200: {
          description: 'User details',
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            avatar: { type: 'string' },
            emailVerified: { type: 'boolean' },
            disabled: { type: 'boolean' },
            role: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        404: {
          description: 'User not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Params: GetUserParams }>, reply: FastifyReply) => {
    try {
      const user = await UserService.findById(request.params.id);
      
      if (!user) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'User not found',
        });
      }

      return user;
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while fetching the user',
      });
    }
  });

  // Get all users (admin only)
  fastify.get('/', {
    schema: {
      tags: ['Users'],
      summary: 'Get all users',
      description: 'Get a paginated list of all users (admin only)',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
        },
      },
      response: {
        200: {
          description: 'List of users',
          type: 'object',
          properties: {
            users: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  email: { type: 'string' },
                  name: { type: 'string' },
                  avatar: { type: 'string' },
                  emailVerified: { type: 'boolean' },
                  disabled: { type: 'boolean' },
                  role: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' },
                },
              },
            },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
          },
        },
      },
    },
  }, async (request: FastifyRequest<{ Querystring: GetUsersQuery }>, reply: FastifyReply) => {
    try {
      const { page = 1, limit = 10 } = request.query;
      const result = await UserService.getAllUsers(page, limit);
      
      return {
        ...result,
        page,
        limit,
      };
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while fetching users',
      });
    }
  });

  // Toggle user status (admin only)
  fastify.patch('/:id/status', {
    schema: {
      tags: ['Users'],
      summary: 'Toggle user status',
      description: 'Enable or disable a user account (admin only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: {
        type: 'object',
        properties: {
          disabled: { type: 'boolean' },
        },
        required: ['disabled'],
      },
    },
  }, async (request: FastifyRequest<{ Params: GetUserParams; Body: ToggleUserStatusBody }>, reply: FastifyReply) => {
    try {
      const user = await UserService.toggleUserStatus(request.params.id, request.body.disabled);
      return user;
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while updating user status',
      });
    }
  });

  // Update user role (admin only)
  fastify.patch('/:id/role', {
    schema: {
      tags: ['Users'],
      summary: 'Update user role',
      description: 'Update a user\'s role (admin only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: {
        type: 'object',
        properties: {
          role: { type: 'string', enum: ['user', 'admin'] },
        },
        required: ['role'],
      },
    },
  }, async (request: FastifyRequest<{ Params: GetUserParams; Body: UpdateUserRoleBody }>, reply: FastifyReply) => {
    try {
      const user = await UserService.updateUserRole(request.params.id, request.body.role);
      return user;
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while updating user role',
      });
    }
  });
}
